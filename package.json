{"name": "bolt-expo-starter", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "build:ios": "npm run pre-build-check && eas build --platform ios", "build:android": "npm run pre-build-check && eas build --platform android", "build:all": "npm run pre-build-check && eas build --platform all", "submit:ios": "eas submit --platform ios", "submit:android": "eas submit --platform android", "lint": "expo lint", "type-check": "tsc --noEmit", "pre-build-check": "node scripts/pre-build-check.js", "production-test": "node scripts/production-simulation-test.js", "deep-validation": "node scripts/deep-production-validation.js", "expo-test": "node scripts/expo-development-test.js", "fix-imports": "node scripts/fix-imports.js", "test-audio": "node scripts/test-audio-url.js", "test-tracks": "node scripts/test-supabase-tracks.js", "android": "expo run:android", "ios": "expo run:ios"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@lucide/lab": "^0.1.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/slider": "4.5.6", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@supabase/supabase-js": "^2.53.0", "expo": "^53.0.0", "expo-av": "^15.1.7", "expo-blur": "~14.1.3", "expo-camera": "~16.1.5", "expo-clipboard": "^7.1.5", "expo-constants": "~17.1.3", "expo-font": "~13.3.2", "expo-haptics": "~14.1.3", "expo-image": "^2.4.0", "expo-linear-gradient": "~14.1.3", "expo-linking": "~7.1.3", "expo-notifications": "~0.31.4", "expo-router": "~5.1.4", "expo-splash-screen": "~0.30.6", "expo-status-bar": "~2.2.2", "expo-symbols": "~0.4.3", "expo-system-ui": "~5.0.5", "expo-video": "^2.2.2", "expo-web-browser": "~14.2.0", "lucide-react-native": "^0.475.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}}