<!DOCTYPE html>
<html lang="sr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Privacy Policy - Псалтир</title>
    <style>
        :root {
            --primary-bg: #ffffff;
            --secondary-bg: #f8f9fa;
            --text-primary: #333333;
            --text-secondary: #666666;
            --accent-gold: #D4AF37;
            --border-color: #e0e0e0;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        [data-theme="dark"] {
            --primary-bg: #0C0C0C;
            --secondary-bg: #1A1A1A;
            --text-primary: #FFFFFF;
            --text-secondary: #CCCCCC;
            --accent-gold: #D4AF37;
            --border-color: #333333;
            --shadow-color: rgba(255, 255, 255, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
            background-color: var(--primary-bg);
            color: var(--text-primary);
            line-height: 1.6;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px 0;
            border-bottom: 2px solid var(--accent-gold);
        }

        .header h1 {
            color: var(--accent-gold);
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .content {
            background-color: var(--secondary-bg);
            padding: 30px;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            box-shadow: 0 4px 6px var(--shadow-color);
        }

        .section {
            margin-bottom: 30px;
        }

        .section h2 {
            color: var(--accent-gold);
            font-size: 1.5rem;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .section h3 {
            color: var(--text-primary);
            font-size: 1.2rem;
            margin-bottom: 10px;
            margin-top: 20px;
            font-weight: 600;
        }

        .section p {
            color: var(--text-secondary);
            margin-bottom: 15px;
            font-size: 1rem;
        }

        .section ul {
            margin-left: 20px;
            margin-bottom: 15px;
        }

        .section li {
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: var(--accent-gold);
            color: var(--primary-bg);
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            opacity: 0.8;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px 0;
            border-top: 1px solid var(--border-color);
            color: var(--text-secondary);
        }

        .highlight-box {
            background-color: var(--primary-bg);
            border-left: 4px solid var(--accent-gold);
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">🌙</button>
    
    <div class="container">
        <div class="header">
            <h1>Privacy Policy</h1>
            <p>Политика приватности - Псалтир апликација</p>
        </div>

        <div class="content">
            <div class="section">
                <h2>1. Увод</h2>
                <p>Ова Политика приватности објашњава како Псалтир апликација ("Апликација") прикупља, користи и штити ваше личне податке. Ваша приватност је важна за нас, и посвећени смо заштити ваших података.</p>
            </div>

            <div class="section">
                <h2>2. Информације које прикупљамо</h2>
                
                <h3>2.1 Информације које НЕ прикупљамо</h3>
                <p>Важно је нагласити да наша Апликација НЕ прикупља следеће типове података:</p>
                <ul>
                    <li>Личне информације (име, презиме, е-пошта, телефон)</li>
                    <li>Локацијске податке</li>
                    <li>Информације о уређају</li>
                    <li>Историју прегледања</li>
                    <li>Било какве податке који се могу повезати са вашим идентитетом</li>
                </ul>

                <h3>2.2 Локално чување података</h3>
                <p>Једини подаци који се чувају су:</p>
                <ul>
                    <li>Омиљени псалми и молитве (чувају се локално на вашем уређају)</li>
                    <li>Подешавања Апликације (тема, јачина звука)</li>
                    <li>Статистика активности (број дана коришћења)</li>
                </ul>
            </div>

            <div class="section">
                <h2>3. Како користимо информације</h2>
                <p>Информације које се чувају локално користе се искључиво за:</p>
                <ul>
                    <li>Побољшање вашег искуства коришћења Апликације</li>
                    <li>Приказивање ваших омиљених садржаја</li>
                    <li>Задржавање ваших подешавања</li>
                    <li>Праћење ваше духовне активности</li>
                </ul>
            </div>

            <div class="section">
                <h2>4. Дељење података</h2>
                <p>Ваши подаци се НЕ деле са трећим странама. Сви подаци остају на вашем уређају и не преносе се на наше сервере или било ком другом сервису.</p>
                
                <div class="highlight-box">
                    <strong>Важно:</strong> Апликација не користи аналитичке алатке, рекламне мреже или било какве треће стране сервисе који би могли прикупити ваше податке.
                </div>
            </div>

            <div class="section">
                <h2>5. Безбедност података</h2>
                <p>Пошто се сви подаци чувају локално на вашем уређају, безбедност зависи од ваших подешавања уређаја. Препоручујемо:</p>
                <ul>
                    <li>Коришћење јаке лозинке за уређај</li>
                    <li>Редовно ажурирање оперативног система</li>
                    <li>Коришћење антивирусног софтвера</li>
                    <li>Одржавање резервних копија важних података</li>
                </ul>
            </div>

            <div class="section">
                <h2>6. Ваша права</h2>
                <p>Имате право на:</p>
                <ul>
                    <li>Приступ свим подацима који се чувају у Апликацији</li>
                    <li>Брисање свих података (кроз подешавања уређаја)</li>
                    <li>Експорт ваших омиљених садржаја</li>
                    <li>Питања о нашем приступу приватности</li>
                </ul>
            </div>

            <div class="section">
                <h2>7. Деца и приватност</h2>
                <p>Апликација није намењена деци млађој од 13 година. Не прикупљамо свесно информације од деце млађе од 13 година. Ако сте родитељ и сматрате да ваше дете је дало личне информације, контактирајте нас.</p>
            </div>

            <div class="section">
                <h2>8. Измене Политике приватности</h2>
                <p>Можемо повремено ажуритати ову Политику приватности. О изменама ћемо вас обавестити кроз Апликацију. Наставком коришћења Апликације након измена, прихватате нову Политику приватности.</p>
            </div>

            <div class="section">
                <h2>9. Контакт</h2>
                <p>Ако имате питања о овој Политици приватности или о томе како Апликација обрађује ваше податке, можете се обратити аутору преко:</p>
                <ul>
                    <li>GitHub профила: <a href="https://github.com/lakarijeprvovencani" style="color: var(--accent-gold);">lakarijeprvovencani</a></li>
                    <li>Јавно доступних канала комуникације</li>
                </ul>
            </div>

            <div class="section">
                <h2>10. Коначне одредбе</h2>
                <p>Ова Политика приватности представља потпуни споразум о томе како Апликација обрађује ваше податке. Коришћењем Апликације прихватате ову Политику приватности.</p>
            </div>
        </div>

        <div class="footer">
            <p>Последње ажурирање: Јануар 2025</p>
            <p>© 2025 Немања Лакић - Псалтир апликација</p>
        </div>
    </div>

    <script>
        function toggleTheme() {
            const body = document.body;
            const button = document.querySelector('.theme-toggle');
            
            if (body.getAttribute('data-theme') === 'dark') {
                body.removeAttribute('data-theme');
                button.textContent = '🌙';
                localStorage.setItem('theme', 'light');
            } else {
                body.setAttribute('data-theme', 'dark');
                button.textContent = '☀️';
                localStorage.setItem('theme', 'dark');
            }
        }

        // Check for saved theme preference or default to light mode
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.body.setAttribute('data-theme', 'dark');
            document.querySelector('.theme-toggle').textContent = '☀️';
        }
    </script>
</body>
</html> 