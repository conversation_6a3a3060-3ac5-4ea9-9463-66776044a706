/*
  # Add Kathisma 9 and Psalms 64-69

  1. New Tables
    - Updates `kathismas` table with Kathisma 9 data
    - Updates `psalms` table with Psalms 64-69

  2. Security
    - Uses existing RLS policies
    - No changes to security settings

  3. Changes
    - Adds Kathisma 9 with description and psalm numbers
    - Adds 6 new psalms (64-69) with full content
    - Each psalm includes title, subtitle, content array, and notes
*/

-- Insert or update Kathisma 9
INSERT INTO public.kathismas (id, title, psalms_range, description, psalm_numbers)
VALUES (9, '9. Катисма', 'Псалме 64-69', 'Девета катисма садржи псалме који говоре о Божјој милости и благословима, о васкрсењу Христовом, о страдањима праведника и о Божјој помоћи у невољи. Посебно се истиче псалам 67 који почиње речима "Да васкрсне Бог" и псалам 68 који пророчки говори о Христовим страдањима.', ARRAY[64,65,66,67,68,69])
ON CONFLICT (id) DO UPDATE SET
  title = EXCLUDED.title,
  psalms_range = EXCLUDED.psalms_range,
  description = EXCLUDED.description,
  psalm_numbers = EXCLUDED.psalm_numbers;

-- Insert or update Psalm 64
INSERT INTO public.psalms (id, title, subtitle, content, kathisma_id, note)
VALUES (64, 'Псалам 64.', 'За крај, Псалам Давидов, песма; (Јеремије и Језекиља), од народа из расејања, када имађаху излазити. (МТ: Псалам 65)', ARRAY[
'ТЕБИ приличи песма, Боже, на Сиону,',
'и Теби ће се одати молитва у Јерусалиму.',
'Услиши молитву моју;',
'к Теби ће свако тело доћи.',
'Речи безаконика надјачаше нас,',
'и безбоштва наша Ти ћеш очистити.',
'Блажен је кога си изабрао и примио,',
'настаниће се у дворима Твојим.',
'Испунићемо се добрима дома Твога;',
'свет је Храм Твој, чудесан у правди.',
'Услиши нас, Боже, Спаситељу наш,',
'надо свих крајева земаљских,',
'и оних на мору далеком;',
'Који припрема горе снагом Својом,',
'Који је препојасан силом,',
'Који потреса дубину морску;',
'шум валова морских (ко ће поднети)?',
'Смутиће се народи,',
'и устрашиће се који живе по крајевима (земље) од чудеса Твојих;',
'изласке јутра и вечера (Ти) украшаваш.',
'Посетио си земљу, и напојио си је,',
'умножио си да је обогатиш.',
'Река Божија испуни се водама;',
'припремио си храну њихову, јер је таква припрема Твоја.',
'Бразде њене напој,',
'умножи плодове (=жита) њена;',
'у капљама њеним обрадоваће се растући.',
'Благословићеш венац године благости Твоје,',
'и поља Твоја испуниће се плодношћу;',
'обогатиће се лепоте пустиње,',
'и брегови ће се опасати весељем.',
'Оденуше се овнови овчији,',
'и долине ће умножити пшеницу;',
'кликтаће, јер ће химне певати.'
], 9, 'Псалам захвалности за Божје благослове и плодност земље.')
ON CONFLICT (id) DO UPDATE SET
  title = EXCLUDED.title,
  subtitle = EXCLUDED.subtitle,
  content = EXCLUDED.content,
  kathisma_id = EXCLUDED.kathisma_id,
  note = EXCLUDED.note;

-- Insert or update Psalm 65
INSERT INTO public.psalms (id, title, subtitle, content, kathisma_id, note)
VALUES (65, 'Псалам 65.', 'За крај, пасма Псалма, васкрсења.(МТ: Псалам 66)', ARRAY[
'УСКЛИКНИТЕ Господу, сва земљо!',
'Псалмопојте Имену Његовом,',
'дајте славу хвали Његовој.',
'Реците Богу: Како су страшна дела Твоја!',
'У мноштву силе Твоје ласкаће Ти непријатељи Твоји.',
'Сва земља нека се поклони Теби, и псалмопева Теби,',
'нека псалмопоје Имену Твоме, Вишњи!',
'(Диапсалма).',
'',
'Ходите и видите дела Божија;',
'(како) је страшан у саветима (Својим) већма од синова људских.',
'Он, Који преобраћа море у сушу,',
'по реци проћи ће (људи) ногама.',
'Тамо ћемо се обрадовати Њему,',
'Који снагом Својом господари веком;',
'очи Његове надгледају народе;',
'они који (Га) огорчавају нека се у себи не величају.',
'(Диапсалма).',
'',
'Благословите, народи, Бога нашег,',
'и чујте глас хвале Његове,',
'Онога Који је ставио душу моју у живот,',
'и Који није дао ноге моје у спотицање.',
'Јер си нас искушао, Боже;',
'разжегао си нас као што се разжиже сребро;',
'увео си нас у замку,',
'ставио си невоље на плећа наша.',
'Попео си људе на главе наше,',
'прођосмо кроз огањ и воду,',
'и извео си нас у починак.',
'Ући ћемо у дом Твој са свеспаљеницама;',
'одаћу Ти завете моје',
'које изрекоше усне моје,',
'и изговорише уста моја у невољи мојој.',
'Свеспаљенице обилне принећу Ти,',
'са кадом тамјана и овновима;',
'принећу Ти теоце и јарад.',
'(Диапсалма).',
'',
'Ходите, чујте, и казиваћу вам,',
'сви који се бојите Бога,',
'шта је учинио души мојој.',
'К Њему завапих устима мојим,',
'и уздигох (реч) под језиком мојим.',
'Ако неправду видех у срцу моме,',
'нека ме не услиши Господ.',
'Ради тога услиша ме Бог,',
'чу глас мољења мога.',
'Благословен Бог Који не одбаци молитву моју,',
'и милост Своју од мене.'
], 9, 'Псалам радости и хвале за Божја дела и васкрсење.')
ON CONFLICT (id) DO UPDATE SET
  title = EXCLUDED.title,
  subtitle = EXCLUDED.subtitle,
  content = EXCLUDED.content,
  kathisma_id = EXCLUDED.kathisma_id,
  note = EXCLUDED.note;

-- Insert or update Psalm 66
INSERT INTO public.psalms (id, title, subtitle, content, kathisma_id, note)
VALUES (66, 'Псалам 66.', 'За крај, у химнама, Псалам песме, (Давидов).(МТ: Псалам 67)', ARRAY[
'БОЖЕ, смилуј се на нас и благослови нас,',
'јави светло лице Твоје на нас,',
'и помилуј нас.',
'(Диапсалма).',
'',
'Да буде познат на земљи пут Твој,',
'у свима народима спасење Твоје.',
'Нека Те исповедају и хвале народи, Боже,',
'нека Те исповедају и хвале народи сви.',
'Нека се узвеселе и обрадују народи,',
'јер ћеш судити народе у правди,',
'и народе ћеш одвести на земљу.',
'(Диапсалма).',
'',
'Нека Те исповедају и хвале народи, Боже,',
'нека Те исповедају и хвале народи сви.',
'Земља даде плод свој;',
'благослови нас Боже, Боже наш.',
'Благослови нас, Боже,',
'и нека се боје Њега сви крајеви земаљски.',
'Слава:'
], 9, 'Псалам молитве за благослов и да сви народи познају Бога.')
ON CONFLICT (id) DO UPDATE SET
  title = EXCLUDED.title,
  subtitle = EXCLUDED.subtitle,
  content = EXCLUDED.content,
  kathisma_id = EXCLUDED.kathisma_id,
  note = EXCLUDED.note;

-- Insert or update Psalm 67
INSERT INTO public.psalms (id, title, subtitle, content, kathisma_id, note)
VALUES (67, 'Псалам 67.', 'За крај, Давидов Псалам песме, (васкрсења).(МТ: Псалам 68)', ARRAY[
'ДА ВАСКРСНЕ Бог и да се расеју непријатељи Његови,',
'и да беже од лица Његовог (сви) који Га мрзе.',
'Као што ишчезава дим, да ишчезну;',
'као што се топи восак пред лицем огња,',
'тако да погину грешници од лица Божјег.',
'И праведници да се узвеселе,',
'да се обрадују пред Богом,',
'да се насладе у весељу.',
'Певајте Богу, псалмопојте Имену Његовом;',
'пут учините Ономе Који је узишао на запад, Господ је име Њему,',
'и обрадујте се пред Њим.',
'Нека се потресу (сви) од лица Његовог -',
'Оца сирочади и Судије удовица.',
'Бог је у месту светоме Своме.',
'Бог настањује усамљене у дом,',
'Он изводи оковане у јунаштву,',
'такође (и) огорчујуће који обитавају у гробовима.',
'Боже, када си Ти исходио пред народом Твојим,',
'када си Ти пролазио кроз пустињу,',
'(Диапсалма).',
'',
'земља се потресе, јер и небеса дадоше кишу',
'од лица Бога Синајскога,',
'од лица Бога Израиљева.',
'Кишу добровољну одредио си, Боже, наслеђу Твоме (у земљи обећаној);',
'и она ослаби, а Ти си је утврдио.',
'Животиње Твоје бораве у њој;',
'припремио си (је) у доброти Твојој сиромаху, Боже.',
'Господ ће дати реч онима који благовесте снагом многом.',
'Цар над војскама (народа) љубљенога,',
'и лепотом дома разделиће плен.',
'Ако починете усред наслеђа (земље обећане),',
'(бићете као) крила голубице посребрењена,',
'и плећа њена у сјајности злата.',
'(Диапсалма).',
'',
'Када Наднебески разгоњаше цареве по њој (=земљи Хананској),',
'заснежи се на Селмону.',
'Гора Божија (Васанска), гора богата,',
'гора бујна, гора хумовита.',
'Зашто завидите горе богате,',
'гори на којој изволе Бог да обитава на њој?',
'Јер ће се Господ настанити (на њој) до краја.',
'Колесница Божија десетохиљадита,',
'хиљаде управљајућих;',
'Господ је на њима на Синају, у светињи.',
'Узишао си на висину, запленио си плен,',
'узео си даре у људима,',
'јер се и непокорни за настањење (покорише).',
'Господ Бог благословен,',
'благословен Господ из дана у дан;',
'помоћи ће нам (на путу) Бог спасења наших.',
'(Диапсалма).',
'',
'Бог наш, Бог је Који спасава;',
'и Господа Господа врата су смрти.',
'Али ће Бог скршити главе непријатеља Његових,',
'врх темена оних који ходе у преступима својим.',
'Рече Господ: Из (горе) Васана ћу вратити (непријатеље),',
'вратићу их из дубина морских;',
'да се омочи нога твоја у крви,',
'језик паса твојих од крви непријатеља.',
'Виђени бише покрети Твоји, Боже,',
'покрети (певача) Бога мојега, Цара Који је у Светињи (храма).',
'Пристигоше начелници близу псалмопојућих,',
'усред девојака са бубњевима.',
'У црквама благосиљајте Бога,',
'Господа са извора Израиљевих!',
'Тамо је (у хору) Венијамин најмлађи у иступању,',
'кнезови Јудини вође њихове,',
'кнезови Завулонови, кнезови Нефталимови.',
'Заповеди, Боже, силом Твојом;',
'оснажи, Боже, ово што си учинио међу нама.',
'Ради Храма Твога у Јерусалиму,',
'Теби ће донети цареви дарове.',
'Запрети зверима из трске (ритова Египта),',
'крдо бикова међу јуницама народа,',
'да не (нападну и) затворе опробане као сребро;',
'расеј народе који хоће ратове.',
'Доћи ће посланици из Египта,',
'Етиопија ће пружити руку своју Богу.',
'Царства земаљска, певајте Богу, псалмопојте Господу.',
'(Диапсалма).',
'',
'Певајте Богу Који је усео на небо небеско на истоку;',
'ево даће гласом Својим глас силе.',
'Дајте славу Богу;',
'на Израиљу је великолепност Његова,',
'и сила Његова у облацима.',
'Диван је Бог у светима Својима,',
'Бог Израиљев, Он ће дати силу и утврђење народу Своме.',
'Благословен Бог.',
'Слава:'
], 9, 'Васкршњи псалам - "Да васкрсне Бог" - о Христовом васкрсењу и победи.')
ON CONFLICT (id) DO UPDATE SET
  title = EXCLUDED.title,
  subtitle = EXCLUDED.subtitle,
  content = EXCLUDED.content,
  kathisma_id = EXCLUDED.kathisma_id,
  note = EXCLUDED.note;

-- Insert or update Psalm 68
INSERT INTO public.psalms (id, title, subtitle, content, kathisma_id, note)
VALUES (68, 'Псалам 68.', 'За крај, за оне који ће се изменити, Давидов. (МТ: Псалам 69)', ARRAY[
'СПАСИ МЕ, Боже, јер дођоше воде до душе моје.',
'Упадох у блато дубоко, и нема дна;',
'дођох у дубине морске, и бура потопи ме.',
'Уморих се вичући, промукну грло моје,',
'ишчилеше очи моје од надања на Бога мога.',
'Умножише се већма од власи главе моје они који ме мрзе узалуд;',
'оснажише се непријатељи моји, они који ме прогоне неправедно;',
'оно што не узимах, то враћах.',
'Боже, Ти си познао безумље моје,',
'и прегрешења моја од Тебе се не скрише.',
'Нека се не посраме због мене они који стрпљиво чекају Тебе,',
'Господе, Господе над војскама;',
'нити да се постиде због мене они који Те траже, Боже Израиљев.',
'Јер ради Тебе подношах ругање,',
'стид покри лице моје.',
'Отуђен постадох браћи својој,',
'и странац синовима матере моје.',
'Јер ревност за дом Твој изједе ме,',
'и ругања оних који Тебе руже падоше на мене.',
'И покрих постом душу моју,',
'и постадох себи за поругу.',
'И узех за одећу моју врећу,',
'и постадох њима за причу.',
'Нада мном уживаху они што седе на вратима,',
'и о мени певаху винопије.',
'А ја молитвом својом (хитах) к Теби Господе;',
'време је благовољења, Боже, у мноштву милости Твоје;',
'услиши ме, у истини спасења Твога.',
'Спаси ме од блата, да не потонем;',
'да се избавим од оних који ме мрзе, и од вода дубоких.',
'Да ме не потопи бура водена,',
'нити да ме прогута бездан,',
'нити да бунар затвори нада мном уста своја.',
'Услиши ме, Господе, јер је блага милост Твоја;',
'по мноштву милосрђа Твога погледај на ме.',
'Не одврати лице Твоје',
'од слуге Твога,',
'јер сам жалостан, брзо ме услиши.',
'Погледај на душу моју, и избави је;',
'ради непријатеља мојих избави ме.',
'Јер Ти знаш поругу моју,',
'и посрамљење моје, и стид мој;',
'пред Тобом су сви тлачитељи моји.',
'Увреду очекиваше душа моја и муку;',
'и очекивах сажаљивача, и не беше (га),',
'и тешитеља, и не нађох (га).',
'И дадоше ми за храну жуч,',
'и у жеђи мојој напојише ме оцтом.',
'Нека буде трпеза њихова пред њима за замку,',
'и за узвраћај, и за саблазан.',
'Нека се помраче очи њихове да не гледају,',
'и леђа њихова заувек погури;',
'излиј на њих гњев Твој,',
'и јарост гњева Твога нека их снађе.',
'Нека буде двор њихов пуст,',
'и у насељима њиховим нека не буде становника.',
'Јер онога кога си Ти ранио, они прогањаху,',
'и на бол рана мојих (бол) додаваху.',
'Додај безакоње на безакоње њихово,',
'и да не уђу у правду Твоју.',
'Нек буду избрисани из књиге живих,',
'и са праведнима нека се не запишу.',
'Сиромах и болник ја јесам;',
'спасење Твоје, Боже, нека ме прихвати.',
'Хвалићу Име Бога мога са песмом,',
'величаћу Га у похвали;',
'и (то) ће угодно бити Богу већма од млада телета,',
'које израста рогове и папке.',
'Видите сиромаси, и обрадујте се;',
'тражите Бога, и жива ће бити душа ваша.',
'Јер услиша Господ убоге,',
'и сужње Своје не понизи.',
'Нека Га хвале небеса и земља,',
'море и све што се у њима креће.',
'Јер ће Бог спасити Сион,',
'и изградиће се градови Јудеје.',
'И настаниће се тамо, и наследиће је (народ Твој);',
'и семе слугу Твојих поседоваће је (=Јудеју),',
'и који љубе Име Твоје настаниће се у њој.'
], 9, 'Месијански псалам о Христовим страдањима - пророчанство о распећу.')
ON CONFLICT (id) DO UPDATE SET
  title = EXCLUDED.title,
  subtitle = EXCLUDED.subtitle,
  content = EXCLUDED.content,
  kathisma_id = EXCLUDED.kathisma_id,
  note = EXCLUDED.note;

-- Insert or update Psalm 69
INSERT INTO public.psalms (id, title, subtitle, content, kathisma_id, note)
VALUES (69, 'Псалам 69.', 'За крај, Давидов, за спомен, да ме спаси Господ. (МТ: Псалам 70)', ARRAY[
'БОЖЕ, на помоћ моју пази;',
'Господе, да ми помогнеш похитај.',
'Нека се постиде и посраме који траже душу моју;',
'нека се врате натраг, и нек се посраме који ми хоће зала;',
'да се поврате одмах постиђени, који ми говоре: добро, добро!',
'Нека се обрадују и узвеселе због Тебе сви који те траже, Господе,',
'и да говоре свагда они што љубе спасење Твоје: Да се узвелича Господ!',
'А ја сам ништ и убог, Боже, помози ми.',
'Помоћник мој и Избавитељ мој јеси Ти, Господе, не закасни.',
'Слава, И сада: Алилуја!'
], 9, 'Кратка молитва за брзу помоћ и избављење од непријатеља.')
ON CONFLICT (id) DO UPDATE SET
  title = EXCLUDED.title,
  subtitle = EXCLUDED.subtitle,
  content = EXCLUDED.content,
  kathisma_id = EXCLUDED.kathisma_id,
  note = EXCLUDED.note;